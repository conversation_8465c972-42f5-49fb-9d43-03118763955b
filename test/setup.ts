import { afterAll, afterEach, beforeAll, beforeEach, vi } from 'vitest';

beforeAll(async () => {
  // テスト開始前のセットアップ
  console.log('Setting up tests...');
});

afterAll(async () => {
  // テスト終了後のクリーンアップ
  console.log('Cleaning up tests...');
});

beforeEach(async () => {
  vi.clearAllMocks();

  await Promise.all([
    vPrismaDelegate.handleTestEvent({ name: 'test_start' }),
    vPrismaDelegate.handleTestEvent({ name: 'test_fn_start' }),
  ]);
});

afterEach(async () => {
  vi.useRealTimers();

  await Promise.all([
    vPrismaDelegate.handleTestEvent({ name: 'test_done' }),
    vPrismaDelegate.handleTestEvent({
      name: 'test_fn_success',
      test: { parent: null },
    }),
  ]);
});
