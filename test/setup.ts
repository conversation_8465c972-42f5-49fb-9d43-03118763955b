import { afterAll, afterEach, beforeAll, beforeEach, vi } from 'vitest';
import { initialize } from './factories/fabbrica';
import { prisma } from './util';

beforeAll(async () => {
  // テスト開始前のセットアップ
  console.log('Setting up tests...');

  // Fabbricaの初期化
  initialize({ prisma });
});

afterAll(async () => {
  // テスト終了後のクリーンアップ
  console.log('Cleaning up tests...');
  await prisma.$disconnect();
});

beforeEach(async () => {
  vi.clearAllMocks();
});

afterEach(async () => {
  vi.useRealTimers();
});
