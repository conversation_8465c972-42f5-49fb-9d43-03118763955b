import { parse, v7 as uuidv7 } from 'uuid';
import { defineStableTmTransportDailyRecordFactory } from './fabbrica';
import { StableFactory } from './stable_factory';
import { SectionFactory } from './stable_tm_section_factory';

export const TransportDailyRecordFactory = defineStableTmTransportDailyRecordFactory({
  defaultData: ({ seq }) => ({
    transportDailyRecordId: Buffer.from(parse(uuidv7(undefined, undefined, seq))),
    section: SectionFactory,
    stable: StableFactory,
  }),
});
