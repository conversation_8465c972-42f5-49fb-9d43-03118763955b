import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';
import { getUnconfirmedRecordsBeforeDate } from './stable_transport_daily_record_repository';

export const testRepository = async (): Promise<void> => {
  const prisma = new PrismaClient();

  try {
    await prisma.$connect();
    logger.info('Connected to database for repository testing');

    // Test with a specific date
    const testDate = new Date('2024-12-31');
    logger.info(`Testing getUnconfirmedRecordsBeforeDate with date: ${testDate.toISOString()}`);

    const result = await getUnconfirmedRecordsBeforeDate(testDate, prisma);

    if (result.isOk()) {
      const records = result.value;
      logger.info(`Successfully retrieved ${records.length} unconfirmed records`, {
        recordCount: records.length,
        sampleRecord:
          records.length > 0
            ? {
                year: records[0]?.year,
                month: records[0]?.month,
                day: records[0]?.day,
                isConfirmed: records[0]?.isConfirmed,
              }
            : null,
      });
    } else {
      const error = result.error;
      logger.error('Failed to retrieve records', {
        errorName: error.name,
        errorMessage: error.message,
      });
    }
  } catch (error) {
    logger.error('Repository test failed', { error });
  } finally {
    await prisma.$disconnect();
    logger.info('Disconnected from database');
  }
};
