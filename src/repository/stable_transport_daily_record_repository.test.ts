import type { PrismaClient } from '@prisma/client';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { DatabaseError } from './database_error';
import { getUnconfirmedRecordsBeforeDate } from './stable_transport_daily_record_repository';

// Prismaクライアントのモック
const mockPrismaClient = {
  stableTmTransportDailyRecord: {
    findMany: vi.fn(),
  },
  $disconnect: vi.fn(),
} as unknown as PrismaClient;

describe('stable_transport_daily_record_repository', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('getUnconfirmedRecordsBeforeDate', () => {
    it('指定された日付以前の未確認レコードを正常に取得できる', async () => {
      // Arrange
      const testDate = new Date('2024-12-31');
      const mockRecords = [
        {
          transportDailyRecordId: Buffer.from('test-id-1'),
          transportDailyRecordInternalId: BigInt(1),
          sectionId: Buffer.from('section-1'),
          stableUuid: Buffer.from('stable-1'),
          year: 2024,
          month: 12,
          day: 30,
          isConfirmed: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          transportDailyRecordId: Buffer.from('test-id-2'),
          transportDailyRecordInternalId: BigInt(2),
          sectionId: Buffer.from('section-2'),
          stableUuid: Buffer.from('stable-2'),
          year: 2024,
          month: 11,
          day: 15,
          isConfirmed: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      vi.mocked(mockPrismaClient.stableTmTransportDailyRecord.findMany).mockResolvedValue(
        mockRecords,
      );

      // Act
      const result = await getUnconfirmedRecordsBeforeDate(testDate, mockPrismaClient);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(2);
        expect(result.value[0]?.year).toBe(2024);
        expect(result.value[0]?.month).toBe(12);
        expect(result.value[0]?.day).toBe(30);
        expect(result.value[0]?.isConfirmed).toBe(false);
      }

      // Prismaクエリが正しく呼ばれているかチェック
      expect(mockPrismaClient.stableTmTransportDailyRecord.findMany).toHaveBeenCalledWith({
        where: {
          isConfirmed: false,
          OR: [
            { year: { lt: 2024 } },
            { year: 2024, month: { lt: 12 } },
            { year: 2024, month: 12, day: { lt: 31 } },
          ],
        },
        orderBy: [{ year: 'asc' }, { month: 'asc' }, { day: 'asc' }],
      });
    });

    it('レコードが存在しない場合は空配列を返す', async () => {
      // Arrange
      const testDate = new Date('2024-01-01');
      vi.mocked(mockPrismaClient.stableTmTransportDailyRecord.findMany).mockResolvedValue([]);

      // Act
      const result = await getUnconfirmedRecordsBeforeDate(testDate, mockPrismaClient);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(0);
      }
    });

    it('データベースエラーが発生した場合はDatabaseErrorを返す', async () => {
      // Arrange
      const testDate = new Date('2024-12-31');
      const dbError = new Error('Database connection failed');
      vi.mocked(mockPrismaClient.stableTmTransportDailyRecord.findMany).mockRejectedValue(dbError);

      // Act
      const result = await getUnconfirmedRecordsBeforeDate(testDate, mockPrismaClient);

      // Assert
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(DatabaseError);
        expect(result.error.message).toContain('Failed to fetch unconfirmed records');
        expect(result.error.message).toContain('Database connection failed');
      }
    });

    it('年をまたぐ日付の検索が正しく動作する', async () => {
      // Arrange
      const testDate = new Date('2025-01-15');
      vi.mocked(mockPrismaClient.stableTmTransportDailyRecord.findMany).mockResolvedValue([]);

      // Act
      await getUnconfirmedRecordsBeforeDate(testDate, mockPrismaClient);

      // Assert
      expect(mockPrismaClient.stableTmTransportDailyRecord.findMany).toHaveBeenCalledWith({
        where: {
          isConfirmed: false,
          OR: [
            { year: { lt: 2025 } },
            { year: 2025, month: { lt: 1 } },
            { year: 2025, month: 1, day: { lt: 15 } },
          ],
        },
        orderBy: [{ year: 'asc' }, { month: 'asc' }, { day: 'asc' }],
      });
    });

    it('月をまたぐ日付の検索が正しく動作する', async () => {
      // Arrange
      const testDate = new Date('2024-03-01');
      vi.mocked(mockPrismaClient.stableTmTransportDailyRecord.findMany).mockResolvedValue([]);

      // Act
      await getUnconfirmedRecordsBeforeDate(testDate, mockPrismaClient);

      // Assert
      expect(mockPrismaClient.stableTmTransportDailyRecord.findMany).toHaveBeenCalledWith({
        where: {
          isConfirmed: false,
          OR: [
            { year: { lt: 2024 } },
            { year: 2024, month: { lt: 3 } },
            { year: 2024, month: 3, day: { lt: 1 } },
          ],
        },
        orderBy: [{ year: 'asc' }, { month: 'asc' }, { day: 'asc' }],
      });
    });
  });
});
